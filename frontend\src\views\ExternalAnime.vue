<!-- 外部动漫浏览页面 - 展示豪华资源的动漫数据 -->
<template>
  <div class="external-anime-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>🎬 在线动漫</h1>
      <p>精选1652部日本动漫，实时更新</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <el-row :gutter="20">
        <!-- 搜索框 -->
        <el-col :span="12">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索动漫名称..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch" :loading="isSearching">
                搜索
              </el-button>
            </template>
          </el-input>
        </el-col>

        <!-- 分类筛选 -->
        <el-col :span="8">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            size="large"
            @change="handleCategoryChange"
          >
            <el-option
              v-for="(id, name) in categories"
              :key="id"
              :label="name"
              :value="name"
            />
          </el-select>
        </el-col>

        <!-- 刷新按钮 -->
        <el-col :span="4">
          <el-button
            type="primary"
            size="large"
            @click="refreshData"
            :loading="isLoading"
          >
            刷新数据
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="animeData">
      <el-row :gutter="20">
        <el-col :span="6">
          <!-- 修复：使用list.length获取实际动漫数量，因为API返回的数据中没有total字段 -->
          <el-statistic title="总动漫数" :value="animeData.list ? animeData.list.length : 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总页数" :value="animeData.pagecount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="当前页" :value="currentPage" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="当前分类" :value="selectedCategory" />
        </el-col>
      </el-row>
    </div>

    <!-- 动漫列表 -->
    <div class="anime-list-section">
      <el-row :gutter="20" v-loading="isLoading">
        <el-col
          :span="8"
          v-for="anime in animeList"
          :key="anime.vod_id"
          class="anime-card-col"
        >
          <el-card
            class="anime-card"
            :body-style="{ padding: '0px' }"
            shadow="hover"
            @click="viewAnimeDetail(anime)"
          >
            <!-- 动漫封面 -->
            <div class="anime-cover">
              <img
                v-if="anime.vod_pic"
                :src="anime.vod_pic"
                :alt="anime.vod_name"
                @error="handleImageError"
              />
              <div v-else class="placeholder-cover">
                <el-icon size="48"><VideoPlay /></el-icon>
                <span>{{ anime.vod_name.charAt(0) }}</span>
              </div>
              <div class="anime-overlay">
                <div class="anime-year" v-if="anime.vod_year">
                  {{ anime.vod_year }}年
                </div>
              </div>
            </div>

            <!-- 动漫信息 -->
            <div class="anime-info">
              <h3 class="anime-title" :title="anime.vod_name">
                {{ anime.vod_name }}
              </h3>
              
              <div class="anime-meta">
                <el-tag size="small" type="primary">
                  {{ anime.type_name }}
                </el-tag>
                <el-tag size="small" type="success" v-if="anime.vod_remarks">
                  {{ anime.vod_remarks }}
                </el-tag>
              </div>

              <div class="anime-time">
                <el-icon><Clock /></el-icon>
                <span>{{ formatTime(anime.vod_time) }}</span>
              </div>

              <div class="anime-sources">
                <el-tag
                  v-for="source in parsePlaySources(anime.vod_play_from)"
                  :key="source"
                  size="mini"
                  type="info"
                >
                  {{ source }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty
        v-if="!isLoading && animeList.length === 0"
        description="暂无动漫数据"
        :image-size="200"
      />
    </div>

    <!-- 分页器 -->
    <div class="pagination-section" v-if="animeData && animeData.pagecount > 1">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="20"
        :total="animeData.list ? animeData.list.length : 0"
        :page-count="animeData.pagecount"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, VideoPlay, Clock } from '@element-plus/icons-vue'
import { externalAPI } from '@/services/api'

const router = useRouter()

// 响应式数据
const isLoading = ref(false)
const isSearching = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('日本动漫')
const currentPage = ref(1)
const animeData = ref(null)
const animeList = ref([])

// 分类数据
const categories = reactive({
  '日本动漫': 25,
  '中国动漫': 24,
  '欧美动漫': 26,
  '动画片': 23
})

// 计算属性
const isSearchMode = computed(() => !!searchKeyword.value.trim())

// 获取动漫列表
const fetchAnimeList = async (page = 1, keyword = null) => {
  try {
    isLoading.value = true
    console.log('📡 获取动漫列表:', { category: selectedCategory.value, page, keyword })

    let response
    if (keyword) {
      // 搜索模式
      console.log('📡 进入搜索模式，调用searchAnime API')
      response = await externalAPI.searchAnime(keyword, page)
      console.log('📡 searchAnime API响应:', response)
    } else {
      // 分类浏览模式
      console.log('📡 进入分类浏览模式，调用getAnimeList API')
      response = await externalAPI.getAnimeList(selectedCategory.value, page)
      console.log('📡 getAnimeList API响应:', response)
    }

    if (response && response.code === 1) {
      animeData.value = response
      animeList.value = response.list || []
      // 修复：使用list.length获取实际动漫数量，因为API返回的数据中没有total字段
      const animeCount = response.list ? response.list.length : 0
      console.log('📡 获取动漫数据成功:', animeCount, '部动漫')
      console.log('📡 动漫列表:', animeList.value)
    } else {
      console.error('📡 API响应异常:', response)
      throw new Error(response?.msg || '获取数据失败')
    }

  } catch (error) {
    console.error('📡 获取动漫列表失败:', error)
    ElMessage.error('获取动漫数据失败: ' + error.message)
    animeList.value = []
  } finally {
    isLoading.value = false
  }
}

// 处理搜索
const handleSearch = async () => {
  console.log('🔍 开始搜索，关键词:', searchKeyword.value)

  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  try {
    isSearching.value = true
    currentPage.value = 1
    console.log('🔍 调用fetchAnimeList，关键词:', searchKeyword.value.trim())
    await fetchAnimeList(1, searchKeyword.value.trim())
    console.log('🔍 搜索完成')
  } catch (error) {
    console.error('🔍 搜索过程中出错:', error)
    ElMessage.error('搜索失败: ' + error.message)
  } finally {
    isSearching.value = false
  }
}


// 处理分类变化
const handleCategoryChange = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchAnimeList(1)
}

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page
  const keyword = isSearchMode.value ? searchKeyword.value.trim() : null
  fetchAnimeList(page, keyword)
  
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 刷新数据
const refreshData = () => {
  const keyword = isSearchMode.value ? searchKeyword.value.trim() : null
  fetchAnimeList(currentPage.value, keyword)
}

// 查看动漫详情
const viewAnimeDetail = (anime) => {
  console.log('查看动漫详情:', anime.vod_name)
  // 跳转到动漫详情页面
  router.push(`/anime/${anime.vod_id}`)
}

// 解析播放源
const parsePlaySources = (vodPlayFrom) => {
  if (!vodPlayFrom) return []
  return vodPlayFrom.split(',').map(source => source.trim())
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间'
  try {
    const date = new Date(timeStr)
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return timeStr
  }
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
  // 显示占位符
  const placeholder = event.target.parentNode.querySelector('.placeholder-cover')
  if (placeholder) {
    placeholder.style.display = 'flex'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAnimeList(1)
})
</script>

<style scoped>
.external-anime-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.search-filter-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.anime-list-section {
  margin-bottom: 30px;
}

.anime-card-col {
  margin-bottom: 20px;
}

.anime-card {
  cursor: pointer;
  transition: transform 0.2s;
  height: 100%;
}

.anime-card:hover {
  transform: translateY(-5px);
}

.anime-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.anime-cover img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s;
}

.anime-card:hover .anime-cover img {
  transform: scale(1.05);
}

.anime-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 20px;
  opacity: 0;
  transition: opacity 0.3s;
}

.anime-card:hover .anime-overlay {
  opacity: 1;
}

.anime-year {
  color: white;
  font-size: 12px;
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.placeholder-cover {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.placeholder-cover .el-icon {
  margin-bottom: 10px;
  opacity: 0.7;
}

.anime-info {
  padding: 15px;
}

.anime-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.anime-meta {
  margin-bottom: 10px;
}

.anime-meta .el-tag {
  margin-right: 5px;
}

.anime-time {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.anime-time .el-icon {
  margin-right: 5px;
}

.anime-sources .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .external-anime-page {
    padding: 10px;
  }
  
  .anime-card-col {
    margin-bottom: 15px;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
