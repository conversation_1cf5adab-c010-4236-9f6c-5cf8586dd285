<!-- 搜索结果页面 - 专门展示搜索结果的页面 -->
<template>
  <div class="search-results-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>搜索结果</h1>
        <p v-if="searchKeyword">关键词: "{{ searchKeyword }}"</p>
      </div>
    </div>

    <!-- 搜索框区域 -->
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索动漫名称..."
        size="large"
        clearable
        @keyup.enter="handleSearch"
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button @click="handleSearch" :loading="isSearching" type="primary">
            搜索
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 搜索统计信息 -->
    <div v-if="searchKeyword && !isLoading" class="search-stats">
      <el-alert
        :title="`找到 ${totalResults} 个相关结果`"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 动漫列表 -->
    <div class="anime-list-section">
      <el-row :gutter="20" v-loading="isLoading">
        <el-col
          :span="6"
          v-for="anime in animeList"
          :key="anime.vod_id"
          class="anime-card-col"
        >
          <el-card
            class="anime-card"
            :body-style="{ padding: '0px' }"
            shadow="hover"
            @click="viewAnimeDetail(anime)"
          >
            <!-- 动漫封面 -->
            <div class="anime-cover">
              <img
                v-if="anime.vod_pic"
                :src="anime.vod_pic"
                :alt="anime.vod_name"
                @error="handleImageError"
              />
              <div v-else class="placeholder-cover">
                <el-icon size="48"><VideoPlay /></el-icon>
                <span>{{ anime.vod_name.charAt(0) }}</span>
              </div>
              <div class="anime-overlay">
                <div class="anime-year" v-if="anime.vod_year">
                  {{ anime.vod_year }}年
                </div>
              </div>
            </div>

            <!-- 动漫信息 -->
            <div class="anime-info">
              <h3 class="anime-title">{{ anime.vod_name }}</h3>
              <div class="anime-meta">
                <el-tag v-if="anime.type_name" size="small" type="info">
                  {{ anime.type_name }}
                </el-tag>
                <el-tag v-if="anime.vod_year" size="small">
                  {{ anime.vod_year }}年
                </el-tag>
              </div>
              <div class="anime-time" v-if="anime.vod_time">
                <el-icon><Clock /></el-icon>
                <span>{{ formatTime(anime.vod_time) }}</span>
              </div>
              <div class="anime-sources" v-if="anime.vod_play_from">
                <el-tag
                  v-for="source in parsePlaySources(anime.vod_play_from)"
                  :key="source"
                  size="small"
                  effect="plain"
                >
                  {{ source }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty
        v-if="!isLoading && animeList.length === 0 && searchKeyword"
        description="未找到相关动漫"
        :image-size="200"
      />

      <!-- 未搜索状态 -->
      <el-empty
        v-if="!isLoading && !searchKeyword"
        description="请输入关键词开始搜索"
        :image-size="200"
      />
    </div>

    <!-- 分页 -->
    <div v-if="animeList.length > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalResults"
        :page-sizes="[12, 24, 36, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        background
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, VideoPlay, Clock } from '@element-plus/icons-vue'
import { externalAPI } from '@/services/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const isLoading = ref(false)
const isSearching = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(24)
const totalResults = ref(0)
const animeList = ref([])

// 从URL参数初始化搜索关键词
const initFromRoute = () => {
  const searchParam = route.query.search
  if (searchParam) {
    searchKeyword.value = searchParam
    console.log('📡 从URL参数获取搜索关键词:', searchParam)
  }
}

// 执行搜索
const performSearch = async (page = 1, keyword = null) => {
  const searchTerm = keyword || searchKeyword.value.trim()
  if (!searchTerm) {
    animeList.value = []
    totalResults.value = 0
    return
  }

  try {
    isLoading.value = true
    console.log('🔍 执行搜索，关键词:', searchTerm, '页码:', page)
    
    // 调用量子资源API搜索
    const response = await externalAPI.searchAnime(searchTerm, page)
    console.log('🔍 搜索API响应:', response)
    
    if (response && response.code === 1) {
      animeList.value = response.list || []
      // 估算总结果数（因为API可能不返回准确的总数）
      totalResults.value = response.list ? response.list.length * response.pagecount : 0
      console.log('🔍 搜索成功，找到', animeList.value.length, '个结果')
    } else {
      animeList.value = []
      totalResults.value = 0
      ElMessage.info('未找到相关动漫')
    }
  } catch (error) {
    console.error('🔍 搜索失败:', error)
    ElMessage.error('搜索失败: ' + error.message)
    animeList.value = []
    totalResults.value = 0
  } finally {
    isLoading.value = false
  }
}

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  try {
    isSearching.value = true
    currentPage.value = 1
    
    // 更新URL参数
    router.push({
      path: '/search',
      query: { search: searchKeyword.value.trim() }
    })
    
    await performSearch(1, searchKeyword.value.trim())
  } catch (error) {
    console.error('🔍 搜索过程中出错:', error)
    ElMessage.error('搜索失败: ' + error.message)
  } finally {
    isSearching.value = false
  }
}

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page
  performSearch(page)
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  performSearch(1)
}

// 查看动漫详情
const viewAnimeDetail = (anime) => {
  console.log('查看动漫详情:', anime.vod_name)
  router.push(`/anime/${anime.vod_id}`)
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return timeStr
  }
}

// 解析播放源
const parsePlaySources = (vodPlayFrom) => {
  if (!vodPlayFrom) return []
  return vodPlayFrom.split(',').map(source => source.trim())
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
  const placeholder = event.target.parentNode.querySelector('.placeholder-cover')
  if (placeholder) {
    placeholder.style.display = 'flex'
  }
}

// 监听路由参数变化
watch(() => route.query.search, (newSearch) => {
  if (newSearch && newSearch !== searchKeyword.value) {
    searchKeyword.value = newSearch
    console.log('📡 路由参数变化，更新搜索关键词:', newSearch)
    currentPage.value = 1
    performSearch(1, newSearch)
  }
}, { immediate: false })

// 组件挂载时初始化
onMounted(() => {
  initFromRoute()
  if (searchKeyword.value.trim()) {
    console.log('📡 组件挂载时发现搜索关键词，自动执行搜索:', searchKeyword.value)
    performSearch(1, searchKeyword.value)
  }
})
</script>

<style scoped>
.search-results-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
}

.search-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.search-input {
  max-width: 600px;
  margin: 0 auto;
  display: block;
}

.search-stats {
  margin-bottom: 30px;
}

.anime-list-section {
  margin-bottom: 30px;
}

.anime-card-col {
  margin-bottom: 20px;
}

.anime-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.anime-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.anime-cover {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.anime-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.anime-card:hover .anime-cover img {
  transform: scale(1.05);
}

.anime-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.anime-card:hover .anime-overlay {
  opacity: 1;
}

.anime-year {
  color: white;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  font-weight: 600;
}

.placeholder-cover {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.placeholder-cover .el-icon {
  margin-bottom: 10px;
  opacity: 0.7;
}

.anime-info {
  padding: 20px;
}

.anime-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.anime-meta {
  margin-bottom: 12px;
}

.anime-meta .el-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.anime-time {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.anime-time .el-icon {
  margin-right: 5px;
}

.anime-sources .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 30px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-results-page {
    padding: 15px;
  }

  .anime-card-col {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .search-results-page {
    padding: 10px;
  }

  .page-header {
    padding: 30px 15px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .anime-cover {
    height: 200px;
  }

  .anime-info {
    padding: 15px;
  }
}
</style>
