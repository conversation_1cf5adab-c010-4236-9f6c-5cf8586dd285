<!-- 动漫列表页面 - 展示所有动漫的列表，支持搜索和筛选 -->
<template>
  <div class="anime-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>动漫大全</h1>
        <p>发现你喜欢的动漫作品</p>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="filter-container">
        <!-- 搜索框 -->
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索动漫名称..."
            size="large"
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #suffix>
              <el-button @click="handleSearch" type="primary" :icon="Search">搜索</el-button>
            </template>
          </el-input>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
          <div class="filter-group">
            <label>分类：</label>
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              @change="handleFilter"
              @clear="handleFilter"
            >
              <el-option label="全部" value=""></el-option>
              <el-option v-for="cat in categories" :key="cat.id" :label="cat.name" :value="cat.name"></el-option>
            </el-select>
          </div>

          <div class="filter-group">
            <label>状态：</label>
            <el-select
              v-model="selectedStatus"
              placeholder="选择状态"
              clearable
              @change="handleFilter"
              @clear="handleFilter"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="连载中" value="ongoing"></el-option>
              <el-option label="已完结" value="completed"></el-option>
              <el-option label="即将播出" value="upcoming"></el-option>
            </el-select>
          </div>

          <div class="filter-group">
            <label>年份：</label>
            <el-select
              v-model="selectedYear"
              placeholder="选择年份"
              clearable
              @change="handleFilter"
              @clear="handleFilter"
            >
              <el-option label="全部" value=""></el-option>
              <el-option v-for="year in years" :key="year" :label="year" :value="year"></el-option>
            </el-select>
          </div>

          <div class="filter-group">
            <label>排序：</label>
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleFilter"
            >
              <el-option label="最新更新" value="latest"></el-option>
              <el-option label="最受欢迎" value="popular"></el-option>
              <el-option label="评分最高" value="rating"></el-option>
              <el-option label="观看最多" value="views"></el-option>
            </el-select>
          </div>

          <!-- 清除筛选按钮 -->
          <div class="filter-group">
            <el-button @click="clearFilters" type="info" plain>
              <el-icon><RefreshLeft /></el-icon>
              清除筛选
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 动漫列表 -->
    <div class="anime-content">
      <div class="content-container">
        <!-- 结果统计 -->
        <div class="result-info">
          <span>共找到 {{ totalCount }} 部动漫</span>
          <div class="view-mode">
            <el-button-group>
              <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="viewMode = 'grid'">
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="viewMode = 'list'">
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>

        <!-- 网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="anime-grid">
          <div v-for="anime in animeList" :key="anime.id" class="anime-card" @click="goToDetail(anime.id)">
            <div class="anime-poster">
              <img :src="anime.poster" :alt="anime.title" />
              <div class="anime-overlay">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
              </div>
              <div class="status-badge" :class="anime.status">{{ getStatusText(anime.status) }}</div>
            </div>
            <div class="anime-info">
              <h4 class="anime-title">{{ anime.title }}</h4>
              <p class="anime-meta">{{ anime.year }} · {{ anime.category }}</p>
              <div class="anime-stats">
                <div class="rating">
                  <el-rate v-model="anime.rating" disabled show-score size="small" />
                </div>
                <div class="episodes">{{ anime.currentEpisodes }}/{{ anime.totalEpisodes || '?' }}集</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="anime-list-view">
          <div v-for="anime in animeList" :key="anime.id" class="anime-list-item" @click="goToDetail(anime.id)">
            <div class="item-poster">
              <img :src="anime.poster" :alt="anime.title" />
            </div>
            <div class="item-content">
              <div class="item-header">
                <h3 class="item-title">{{ anime.title }}</h3>
                <div class="item-rating">
                  <el-rate v-model="anime.rating" disabled show-score size="small" />
                </div>
              </div>
              <p class="item-description">{{ anime.description }}</p>
              <div class="item-meta">
                <span class="meta-item">{{ anime.year }}</span>
                <span class="meta-item">{{ anime.category }}</span>
                <span class="meta-item">{{ anime.currentEpisodes }}/{{ anime.totalEpisodes || '?' }}集</span>
                <span class="status-tag" :class="anime.status">{{ getStatusText(anime.status) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48, 96]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { animeAPI, categoryAPI, handleApiError } from '@/services/api'
import { Search, Grid, List, VideoPlay, RefreshLeft } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const selectedYear = ref('')
const sortBy = ref('latest')
const viewMode = ref('grid')
const currentPage = ref(1)
const pageSize = ref(24)
const totalCount = ref(0)
const loading = ref(false)

const animeList = ref([])
const categories = ref([])
const years = ref([])

// 初始化数据
const initData = async () => {
  try {
    // 获取分类数据
    const categoriesResponse = await categoryAPI.getAll()
    categories.value = categoriesResponse

    // 年份数据
    years.value = Array.from({ length: 10 }, (_, i) => 2024 - i)

    // 获取动漫列表
    await fetchAnimeList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error(handleApiError(error))

    // 使用备用数据
    categories.value = [
      { id: 1, name: '动作' },
      { id: 2, name: '冒险' },
      { id: 3, name: '喜剧' },
      { id: 4, name: '剧情' },
      { id: 5, name: '奇幻' },
      { id: 6, name: '恋爱' },
      { id: 7, name: '科幻' },
      { id: 8, name: '悬疑' }
    ]
    years.value = Array.from({ length: 10 }, (_, i) => 2024 - i)
  }
}

// 获取动漫列表
const fetchAnimeList = async () => {
  try {
    loading.value = true

    let response

    // 根据搜索条件调用不同的API
    if (searchKeyword.value) {
      response = await animeAPI.search(searchKeyword.value, currentPage.value - 1, pageSize.value)
    } else if (selectedCategory.value) {
      // 找到分类ID
      const category = categories.value.find(cat => cat.name === selectedCategory.value)
      if (category) {
        response = await animeAPI.getByCategory(category.id, currentPage.value - 1, pageSize.value)
      } else {
        response = await animeAPI.getAll(currentPage.value - 1, pageSize.value)
      }
    } else if (selectedStatus.value) {
      response = await animeAPI.getByStatus(selectedStatus.value.toUpperCase(), currentPage.value - 1, pageSize.value)
    } else if (selectedYear.value) {
      response = await animeAPI.getByYear(selectedYear.value, currentPage.value - 1, pageSize.value)
    } else {
      response = await animeAPI.getAll(currentPage.value - 1, pageSize.value)
    }

    // 处理响应数据
    if (response.content) {
      // 分页响应
      animeList.value = response.content.map(anime => ({
        ...anime,
        category: anime.status, // 暂时使用状态作为分类显示
        status: anime.status ? anime.status.toLowerCase() : 'unknown'
      }))
      totalCount.value = response.totalElements
    } else if (Array.isArray(response)) {
      // 数组响应
      animeList.value = response.map(anime => ({
        ...anime,
        category: anime.status,
        status: anime.status ? anime.status.toLowerCase() : 'unknown'
      }))
      totalCount.value = response.length
    }

  } catch (error) {
    console.error('获取动漫列表失败:', error)
    ElMessage.error(handleApiError(error))

    // 使用备用数据
    animeList.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    ongoing: '连载中',
    completed: '已完结',
    upcoming: '即将播出'
  }
  return statusMap[status] || status
}

// 跳转到详情页
const goToDetail = (id) => {
  router.push(`/anime/${id}`)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  fetchAnimeList()
}

// 处理筛选
const handleFilter = () => {
  console.log('筛选条件变化:', {
    category: selectedCategory.value,
    status: selectedStatus.value,
    year: selectedYear.value,
    sort: sortBy.value
  })
  currentPage.value = 1 // 重置到第一页
  fetchAnimeList()
}

// 清除所有筛选条件
const clearFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
  selectedStatus.value = ''
  selectedYear.value = ''
  sortBy.value = 'latest'
  currentPage.value = 1

  // 清除URL参数
  router.push({ path: '/anime' })

  // 重新获取数据
  fetchAnimeList()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchAnimeList()
}

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page
  fetchAnimeList()
}

// 从路由参数初始化筛选条件
const initFromRoute = () => {
  const query = route.query
  if (query.search) {
    searchKeyword.value = query.search
  }
  if (query.category) {
    selectedCategory.value = query.category
  }
  if (query.status) {
    selectedStatus.value = query.status
  }
  if (query.year) {
    selectedYear.value = parseInt(query.year)
  }
  if (query.sort) {
    sortBy.value = query.sort
  }
}

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.search !== undefined) {
    searchKeyword.value = newQuery.search || ''
  }
  if (newQuery.category !== undefined) {
    selectedCategory.value = newQuery.category || ''
  }
  if (newQuery.status !== undefined) {
    selectedStatus.value = newQuery.status || ''
  }
  if (newQuery.year !== undefined) {
    selectedYear.value = newQuery.year ? parseInt(newQuery.year) : ''
  }
  if (newQuery.sort !== undefined) {
    sortBy.value = newQuery.sort || 'latest'
  }

  // 重新获取数据
  fetchAnimeList()
}, { immediate: false })

// 组件挂载时初始化
onMounted(async () => {
  // 先初始化路由参数
  initFromRoute()
  // 然后初始化数据
  await initData()
})
</script>

<style scoped>
/* 页面整体样式 */
.anime-list-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.header-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.header-content p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 30px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.search-area {
  margin-bottom: 30px;
}

.search-input {
  max-width: 600px;
}

.filters {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 150px;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  min-width: 50px;
}

.filter-group .el-select {
  min-width: 120px;
}

/* 确保选择框有足够的宽度显示选中的值 */
.filter-group .el-select .el-input {
  min-width: 120px;
}

/* 内容区域 */
.anime-content {
  padding: 40px 0;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.result-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 网格视图 */
.anime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.anime-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.anime-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.anime-poster {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.anime-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.anime-card:hover .anime-poster img {
  transform: scale(1.05);
}

.anime-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none; /* 防止遮罩层阻止点击事件 */
}

.anime-card:hover .anime-overlay {
  opacity: 1;
}

.play-icon {
  font-size: 4rem;
  color: white;
}

.status-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
}

.status-badge.ongoing {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.status-badge.completed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
}

.status-badge.upcoming {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #2c3e50;
}

.anime-info {
  padding: 20px;
}

.anime-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
  line-height: 1.4;
}

.anime-meta {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.anime-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.episodes {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 600;
}

/* 列表视图 */
.anime-list-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.anime-list-item {
  display: flex;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.anime-list-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.item-poster {
  width: 120px;
  height: 160px;
  flex-shrink: 0;
}

.item-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.item-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.item-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 15px;
  flex: 1;
}

.item-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.meta-item {
  color: #95a5a6;
  font-size: 0.9rem;
}

.status-tag {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-tag.ongoing {
  background: #e8f8f5;
  color: #00b894;
}

.status-tag.completed {
  background: #e3f2fd;
  color: #2196f3;
}

.status-tag.upcoming {
  background: #fff3e0;
  color: #ff9800;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-container {
    padding: 0 20px;
  }

  .filters {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }

  .content-container {
    padding: 0 20px;
  }

  .anime-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 20px;
  }

  .anime-list-item {
    flex-direction: column;
  }

  .item-poster {
    width: 100%;
    height: 200px;
  }

  .result-info {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
