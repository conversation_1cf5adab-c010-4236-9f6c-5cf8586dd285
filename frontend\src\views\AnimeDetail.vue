<!-- 动漫详情页面 - 显示单个动漫的详细信息 -->
<template>
  <!-- 加载状态 -->
  <div v-if="loading || !animeData" class="loading-container">
    <el-skeleton :rows="8" animated />
  </div>

  <!-- 详情内容 -->
  <div class="anime-detail-page" v-else>
    <!-- 头部横幅 -->
    <div class="detail-banner" :style="{ backgroundImage: `url(${animeData.banner})` }">
      <div class="banner-overlay"></div>
      <div class="banner-content">
        <div class="anime-poster">
          <img :src="animeData.poster" :alt="animeData.title" />
        </div>
        <div class="anime-info">
          <h1 class="anime-title">{{ animeData.title }}</h1>
          <p class="anime-subtitle" v-if="animeData.originalTitle">{{ animeData.originalTitle }}</p>
          <div class="anime-meta">
            <span class="meta-item">{{ animeData.year }}</span>
            <span class="meta-item">{{ animeData.season }}</span>
            <span class="meta-item status" :class="animeData.status">{{ getStatusText(animeData.status) }}</span>
            <span class="meta-item">{{ animeData.currentEpisodes }}/{{ animeData.totalEpisodes || '?' }}集</span>
          </div>
          <div class="anime-rating">
            <el-rate v-model="animeData.rating" disabled show-score size="large" />
            <span class="rating-count">({{ animeData.ratingCount }}人评分)</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" size="large" @click="startWatch" class="watch-btn">
              <el-icon><VideoPlay /></el-icon>
              开始观看
            </el-button>
            <el-button size="large" @click="toggleFavorite" :type="isFavorited ? 'danger' : ''" class="fav-btn">
              <el-icon><Star /></el-icon>
              {{ isFavorited ? '已收藏' : '收藏' }}
            </el-button>
            <el-button size="large" @click="shareAnime" class="share-btn">
              <el-icon><Share /></el-icon>
              分享
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="detail-content">
      <div class="content-container">
        <div class="content-layout">
          <!-- 左侧主要内容 -->
          <div class="main-content">
            <!-- 简介 -->
            <section class="section">
              <h3 class="section-title">剧情简介</h3>
              <div class="description">
                <p>{{ animeData.description }}</p>
              </div>
            </section>

            <!-- 剧集列表 -->
            <section class="section">
              <h3 class="section-title">剧集列表</h3>
              <div class="episodes-grid">
                <div
                  v-for="episode in episodes"
                  :key="episode.id"
                  class="episode-card"
                  @click="watchEpisode(episode)"
                  :class="{ 'watched': episode.watched }"
                >
                  <div class="episode-thumbnail">
                    <img :src="episode.thumbnail" :alt="`第${episode.episodeNumber}集`" />
                    <div class="episode-overlay">
                      <el-icon class="play-icon"><VideoPlay /></el-icon>
                    </div>
                    <div class="episode-duration">{{ formatDuration(episode.duration) }}</div>
                  </div>
                  <div class="episode-info">
                    <h4>第{{ episode.episodeNumber }}集</h4>
                    <p v-if="episode.title">{{ episode.title }}</p>
                  </div>
                </div>
              </div>
            </section>

            <!-- 评论区 -->
            <section class="section">
              <h3 class="section-title">评论 ({{ comments.length }})</h3>
              <div class="comment-form" v-if="isLoggedIn">
                <el-input
                  v-model="newComment"
                  type="textarea"
                  :rows="3"
                  placeholder="写下你的评论..."
                  class="comment-input"
                />
                <el-button type="primary" @click="submitComment" class="submit-btn">发表评论</el-button>
              </div>
              <div class="comments-list">
                <div v-for="comment in comments" :key="comment.id" class="comment-item">
                  <div class="comment-avatar">
                    <el-avatar :src="comment.user.avatar">{{ comment.user.username[0] }}</el-avatar>
                  </div>
                  <div class="comment-content">
                    <div class="comment-header">
                      <span class="username">{{ comment.user.username }}</span>
                      <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                    </div>
                    <p class="comment-text">{{ comment.content }}</p>
                    <div class="comment-actions">
                      <el-button text size="small" @click="likeComment(comment)">
                        <el-icon><Like /></el-icon>
                        {{ comment.likeCount }}
                      </el-button>
                      <el-button text size="small" @click="replyComment(comment)">回复</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>

          <!-- 右侧信息栏 -->
          <div class="sidebar">
            <!-- 基本信息 -->
            <div class="info-card">
              <h4>基本信息</h4>
              <div class="info-list">
                <div class="info-item">
                  <span class="label">类型：</span>
                  <span class="value">
                    <el-tag v-for="category in animeData.categories" :key="category" size="small">
                      {{ category }}
                    </el-tag>
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">制作公司：</span>
                  <span class="value">{{ animeData.studio || '未知' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">导演：</span>
                  <span class="value">{{ animeData.director || '未知' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">播出时间：</span>
                  <span class="value">{{ animeData.year }}年{{ animeData.season }}</span>
                </div>
                <div class="info-item">
                  <span class="label">观看次数：</span>
                  <span class="value">{{ formatNumber(animeData.viewCount) }}</span>
                </div>
              </div>
            </div>

            <!-- 相关推荐 -->
            <div class="info-card">
              <h4>相关推荐</h4>
              <div class="related-list">
                <div v-for="related in relatedAnime" :key="related.id" class="related-item" @click="goToAnime(related.id)">
                  <img :src="related.poster" :alt="related.title" />
                  <div class="related-info">
                    <h5>{{ related.title }}</h5>
                    <p>{{ related.year }} · {{ related.category }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'
import { animeAPI, episodeAPI, handleApiError } from '@/services/api'
import { VideoPlay, Star, Share } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const animeData = ref(null)
const episodes = ref([])
const comments = ref([])
const relatedAnime = ref([])
const newComment = ref('')
const isFavorited = ref(false)
const loading = ref(false)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 获取动漫详情数据
const fetchAnimeDetail = async () => {
  const animeId = route.params.id

  try {
    loading.value = true
    console.log('开始获取动漫详情，ID:', animeId)

    // 获取动漫详情
    const animeResponse = await animeAPI.getById(animeId)
    console.log('动漫详情响应:', animeResponse)

    // 处理动漫数据
    animeData.value = {
      ...animeResponse,
      categories: animeResponse.categories || [], // 确保categories是数组
      season: getSeasonText(animeResponse.season),
      status: animeResponse.status ? animeResponse.status.toLowerCase() : 'unknown'
    }

    // 增加观看次数（可选，如果失败不影响页面显示）
    try {
      await animeAPI.incrementViewCount(animeId)
    } catch (viewError) {
      console.warn('增加观看次数失败:', viewError)
    }

    // 尝试获取真实剧集数据，失败时使用模拟数据
    try {
      const episodesResponse = await episodeAPI.getByAnimeId(animeId, true)
      if (episodesResponse && episodesResponse.length > 0) {
        episodes.value = episodesResponse.map(episode => ({
          ...episode,
          watched: false, // 暂时设为false，后续可以从用户观看历史中获取
          thumbnail: episode.thumbnail || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
          duration: episode.duration || 1440
        }))
        console.log('获取到真实剧集数据:', episodes.value.length, '集')
      } else {
        throw new Error('没有剧集数据')
      }
    } catch (episodeError) {
      console.warn('获取剧集列表失败，使用模拟数据:', episodeError)
      // 剧集获取失败时使用模拟数据
      episodes.value = Array.from({ length: animeResponse.currentEpisodes || 12 }, (_, i) => ({
        id: i + 1,
        episodeNumber: i + 1,
        title: `第${i + 1}集`,
        description: `第${i + 1}集的精彩内容`,
        thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
        duration: 1440,
        watched: false,
        isPublished: true
      }))
    }

  } catch (error) {
    console.error('获取动漫详情失败:', error)
    ElMessage.error('获取动漫详情失败，请稍后重试')

    // 使用备用数据
    animeData.value = {
      id: animeId,
      title: '动漫详情加载失败',
      originalTitle: '',
      description: '抱歉，无法加载动漫详情信息。请稍后重试。',
      poster: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop',
      banner: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=400&fit=crop',
      year: 2023,
      season: '未知',
      status: 'unknown',
      currentEpisodes: 0,
      totalEpisodes: 0,
      rating: 0,
      ratingCount: 0,
      viewCount: 0,
      categories: [],
      studio: '未知',
      director: '未知'
    }
  } finally {
    loading.value = false
  }

  // 模拟剧集数据
  episodes.value = Array.from({ length: 12 }, (_, i) => ({
    id: i + 1,
    number: i + 1,
    title: `第${i + 1}集标题`,
    thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
    duration: 1440 + Math.floor(Math.random() * 300), // 24分钟左右
    watched: i < 5 // 前5集已观看
  }))

  // 模拟评论数据
  comments.value = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    user: {
      username: `用户${i + 1}`,
      avatar: `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop`
    },
    content: `这是第${i + 1}条评论，非常精彩的动漫作品！`,
    likeCount: Math.floor(Math.random() * 100),
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  }))

  // 获取相关推荐（使用真实的动漫ID）
  try {
    const relatedResponse = await animeAPI.getPopular(5)
    relatedAnime.value = relatedResponse
      .filter(anime => anime.id !== parseInt(animeId)) // 排除当前动漫
      .slice(0, 4) // 最多显示4个
      .map(anime => ({
        id: anime.id,
        title: anime.title,
        poster: anime.poster,
        year: anime.year,
        category: anime.status || '动作'
      }))
  } catch (relatedError) {
    console.warn('获取相关推荐失败:', relatedError)
    // 使用备用的相关推荐（使用真实存在的ID）
    const currentId = parseInt(animeId)
    const availableIds = [1, 2, 3, 4, 5].filter(id => id !== currentId)
    relatedAnime.value = availableIds.slice(0, 4).map((id, i) => ({
      id: id,
      title: `推荐动漫 ${i + 1}`,
      poster: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=140&fit=crop',
      year: 2023,
      category: '动作'
    }))
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    ongoing: '连载中',
    completed: '已完结',
    upcoming: '即将播出',
    unknown: '未知'
  }
  return statusMap[status] || status
}

// 获取季节文本
const getSeasonText = (season) => {
  const seasonMap = {
    SPRING: '春季',
    SUMMER: '夏季',
    AUTUMN: '秋季',
    FALL: '秋季',
    WINTER: '冬季'
  }
  return seasonMap[season] || season || '未知'
}

// 格式化时长
const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化时间
const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (24 * 60 * 60 * 1000))

  if (days === 0) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    if (hours === 0) {
      const minutes = Math.floor(diff / (60 * 1000))
      return `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// 开始观看
const startWatch = () => {
  const firstUnwatched = episodes.value.find(ep => !ep.watched) || episodes.value[0]
  router.push(`/watch/${animeData.value.id}/${firstUnwatched.number}`)
}

// 观看指定剧集
const watchEpisode = (episode) => {
  router.push(`/watch/${animeData.value.id}/${episode.number}`)
}

// 切换收藏状态
const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  // 这里后续会调用API
}

// 分享动漫
const shareAnime = () => {
  // 这里实现分享功能
  console.log('分享动漫')
}

// 提交评论
const submitComment = () => {
  if (!newComment.value.trim()) return

  // 这里后续会调用API提交评论
  console.log('提交评论:', newComment.value)
  newComment.value = ''
}

// 点赞评论
const likeComment = (comment) => {
  comment.likeCount++
  // 这里后续会调用API
}

// 回复评论
const replyComment = (comment) => {
  // 这里实现回复功能
  console.log('回复评论:', comment.id)
}

// 跳转到其他动漫
const goToAnime = (id) => {
  router.push(`/anime/${id}`)
}

// 监听路由参数变化
watch(() => route.params.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    console.log('路由参数变化，从', oldId, '到', newId)
    fetchAnimeDetail()
  }
}, { immediate: false })

// 组件挂载时获取数据
onMounted(() => {
  fetchAnimeDetail()
})
</script>

<style scoped>
/* 页面整体样式 */
.anime-detail-page {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部横幅 */
.detail-banner {
  height: 500px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: flex-end;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.2) 100%);
}

.banner-content {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px 40px;
  display: flex;
  gap: 40px;
  color: white;
}

.anime-poster {
  flex-shrink: 0;
}

.anime-poster img {
  width: 250px;
  height: 350px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.anime-info {
  flex: 1;
  padding-top: 50px;
}

.anime-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.anime-subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 20px;
}

.anime-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.meta-item {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.meta-item.status.ongoing {
  background: rgba(76, 205, 196, 0.8);
}

.meta-item.status.completed {
  background: rgba(52, 152, 219, 0.8);
}

.anime-rating {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.rating-count {
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.watch-btn, .fav-btn, .share-btn {
  padding: 12px 24px;
  font-size: 1.1rem;
  border-radius: 8px;
  font-weight: 600;
}

.watch-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

/* 主要内容 */
.detail-content {
  padding: 60px 0;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 40px;
}

.section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
}

.description p {
  line-height: 1.8;
  color: #555;
  font-size: 1rem;
}

/* 剧集列表 */
.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.episode-card {
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.episode-card:hover {
  transform: translateY(-5px);
  border-color: #667eea;
}

.episode-card.watched {
  opacity: 0.7;
}

.episode-thumbnail {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.episode-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.episode-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.episode-card:hover .episode-overlay {
  opacity: 1;
}

.play-icon {
  font-size: 2rem;
  color: white;
}

.episode-duration {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.episode-info {
  padding: 15px;
  background: #f8f9fa;
}

.episode-info h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  color: #2c3e50;
}

.episode-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* 评论区 */
.comment-form {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.submit-btn {
  align-self: flex-end;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  display: flex;
  gap: 15px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.username {
  font-weight: 600;
  color: #2c3e50;
}

.comment-time {
  font-size: 0.9rem;
  color: #999;
}

.comment-text {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #555;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

/* 侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.info-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.info-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.info-item .label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.info-item .value {
  flex: 1;
  color: #333;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.related-item {
  display: flex;
  gap: 15px;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.related-item:hover {
  background: #f8f9fa;
}

.related-item img {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.related-info h5 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  color: #2c3e50;
}

.related-info p {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    padding: 0 20px 30px;
  }

  .anime-poster img {
    width: 200px;
    height: 280px;
  }

  .anime-title {
    font-size: 2rem;
  }

  .content-container {
    padding: 0 20px;
  }

  .episodes-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }

  .action-buttons {
    justify-content: center;
  }
}

/* 加载状态样式 */
.loading-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}
</style>
