<!-- 用户注册页面 -->
<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-card">
        <!-- 标题 -->
        <div class="register-header">
          <h2>创建账户</h2>
          <p>加入我们的动漫社区</p>
        </div>

        <!-- 注册表单 -->
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="用户名"
              size="large"
              :prefix-icon="User"
              clearable
              @blur="checkUsernameAvailability"
            />
            <div v-if="usernameCheckResult" class="field-hint">
              <span :class="usernameCheckResult.available ? 'success' : 'error'">
                {{ usernameCheckResult.message }}
              </span>
            </div>
          </el-form-item>

          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              type="email"
              placeholder="邮箱地址"
              size="large"
              :prefix-icon="Message"
              clearable
              @blur="checkEmailAvailability"
            />
            <div v-if="emailCheckResult" class="field-hint">
              <span :class="emailCheckResult.available ? 'success' : 'error'">
                {{ emailCheckResult.message }}
              </span>
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="nickname">
            <el-input
              v-model="registerForm.nickname"
              placeholder="昵称（可选）"
              size="large"
              :prefix-icon="Avatar"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="register-button"
              :loading="authStore.isLoading"
              @click="handleRegister"
            >
              {{ authStore.isLoading ? '注册中...' : '注册' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 登录链接 -->
        <div class="login-link">
          <span>已有账户？</span>
          <el-link type="primary" @click="goToLogin">立即登录</el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Message, Avatar } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const registerFormRef = ref()

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  nickname: ''
})

// 字段检查结果
const usernameCheckResult = ref(null)
const emailCheckResult = ref(null)

// 自定义验证器
const validatePassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请确认密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ]
}

// 检查用户名可用性
const checkUsernameAvailability = async () => {
  if (!registerForm.username || registerForm.username.length < 3) {
    usernameCheckResult.value = null
    return
  }

  try {
    const available = await authStore.checkUsername(registerForm.username)
    usernameCheckResult.value = {
      available,
      message: available ? '✓ 用户名可用' : '✗ 用户名已被使用'
    }
  } catch (error) {
    usernameCheckResult.value = {
      available: false,
      message: '检查失败，请稍后重试'
    }
  }
}

// 检查邮箱可用性
const checkEmailAvailability = async () => {
  if (!registerForm.email || !/\S+@\S+\.\S+/.test(registerForm.email)) {
    emailCheckResult.value = null
    return
  }

  try {
    const available = await authStore.checkEmail(registerForm.email)
    emailCheckResult.value = {
      available,
      message: available ? '✓ 邮箱可用' : '✗ 邮箱已被注册'
    }
  } catch (error) {
    emailCheckResult.value = {
      available: false,
      message: '检查失败，请稍后重试'
    }
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 验证表单
    await registerFormRef.value.validate()

    // 检查用户名和邮箱可用性
    if (usernameCheckResult.value && !usernameCheckResult.value.available) {
      ElMessage.error('用户名不可用')
      return
    }

    if (emailCheckResult.value && !emailCheckResult.value.available) {
      ElMessage.error('邮箱不可用')
      return
    }

    // 执行注册
    await authStore.register(registerForm)

    // 注册成功，跳转到首页
    router.push('/')

  } catch (error) {
    console.error('注册处理失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-container {
  width: 100%;
  max-width: 450px;
}

.register-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.register-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.register-form {
  margin-bottom: 20px;
}

.field-hint {
  margin-top: 5px;
  font-size: 12px;
}

.field-hint .success {
  color: #67c23a;
}

.field-hint .error {
  color: #f56c6c;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.login-link span {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-card {
    padding: 30px 20px;
  }

  .register-header h2 {
    font-size: 24px;
  }
}
</style>
