<!-- 主应用组件 - 包含导航栏和路由出口 -->
<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { Search, User, Setting, VideoPlay, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { externalAPI } from '@/services/api'

const router = useRouter()
const authStore = useAuthStore()

// 搜索相关数据
const searchKeyword = ref('')
const searchResults = ref([])
const isSearching = ref(false)
const showSearchResults = ref(false)

// 计算属性
const isLoggedIn = computed(() => authStore.isLoggedIn)
const userInfo = computed(() => authStore.user)

// 实时搜索功能 - 输入时自动搜索
const performRealTimeSearch = async (keyword) => {
  if (!keyword || keyword.length < 2) {
    searchResults.value = []
    showSearchResults.value = false
    return
  }

  try {
    isSearching.value = true
    console.log('🔍 实时搜索开始，关键词:', keyword)

    // 调用量子资源API搜索
    const response = await externalAPI.searchAnime(keyword, 1)
    console.log('🔍 搜索API响应:', response)

    if (response && response.code === 1 && response.list) {
      searchResults.value = response.list.slice(0, 8) // 只显示前8个结果
      showSearchResults.value = true
      console.log('🔍 搜索成功，找到', searchResults.value.length, '个结果')
    } else {
      searchResults.value = []
      showSearchResults.value = keyword.length >= 2 // 只有输入了内容才显示"未找到"
    }
  } catch (error) {
    console.error('🔍 搜索失败:', error)
    searchResults.value = []
    showSearchResults.value = false
  } finally {
    isSearching.value = false
  }
}

// 防抖搜索 - 避免频繁API调用
let searchTimeout = null
const debouncedSearch = (keyword) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    performRealTimeSearch(keyword)
  }, 300) // 300ms延迟
}

// 处理搜索按钮点击或回车 - 跳转到搜索结果页面
const handleSearch = () => {
  const keyword = searchKeyword.value.trim()
  if (!keyword) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  // 关闭搜索结果下拉框
  closeSearchResults()

  // 跳转到搜索结果页面
  router.push(`/search?search=${encodeURIComponent(keyword)}`)
  console.log('🔍 跳转到搜索结果页面，关键词:', keyword)
}

// 关闭搜索结果
const closeSearchResults = () => {
  showSearchResults.value = false
  searchKeyword.value = ''
  searchResults.value = []
}

// 查看动漫详情
const viewAnimeDetail = (anime) => {
  console.log('查看动漫详情:', anime.vod_name)
  // 关闭搜索结果
  closeSearchResults()
  // 跳转到动漫详情页面
  router.push(`/anime/${anime.vod_id}`)
}

// 点击外部关闭搜索结果
const handleClickOutside = (event) => {
  const searchBox = event.target.closest('.search-box')
  if (!searchBox && showSearchResults.value) {
    closeSearchResults()
  }
}

// 组件挂载时添加全局点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 监听搜索关键词变化，实现实时搜索
watch(searchKeyword, (newKeyword) => {
  console.log('🔍 搜索关键词变化:', newKeyword)
  debouncedSearch(newKeyword.trim())
}, { immediate: false })

// 组件卸载时移除事件监听和清理定时器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})

// 用户登出
const handleLogout = async () => {
  await authStore.logout()
  router.push('/')
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/user')
      break
    case 'favorites':
      router.push('/user/favorites')
      break
    case 'history':
      router.push('/user/history')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 导航菜单
const menuItems = [
  { name: '首页', path: '/' },
  { name: '动漫', path: '/anime' },
  { name: '在线动漫', path: '/external' },
  { name: '排行榜', path: '/ranking' },
  { name: '分类', path: '/category' }
]

// 初始化认证状态
onMounted(async () => {
  await authStore.initAuth()
})
</script>

<template>
  <div id="app">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-container">
        <!-- Logo -->
        <div class="logo" @click="$router.push('/')">
          <h1>动漫网站</h1>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            active-class="active"
          >
            {{ item.name }}
          </router-link>
        </nav>

        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="输入动漫名称，实时搜索..."
            @keyup.enter="handleSearch"
            class="search-input"
            clearable
          >
            <template #suffix>
              <el-icon
                @click="handleSearch"
                class="search-icon"
                :class="{ 'searching': isSearching }"
                :title="searchKeyword.trim() ? '查看完整搜索结果' : '搜索'"
              >
                <Search />
              </el-icon>
            </template>
          </el-input>

          <!-- 搜索结果下拉框 -->
          <div v-if="showSearchResults && searchKeyword.trim()" class="search-results-dropdown">
            <div class="search-results-header">
              <span v-if="isSearching">正在搜索...</span>
              <span v-else>搜索结果 ({{ searchResults.length }})</span>
              <el-icon @click="closeSearchResults" class="close-icon">
                <Close />
              </el-icon>
            </div>

            <div v-if="!isSearching && searchResults.length === 0" class="no-results">
              <p>未找到相关动漫</p>
              <p class="no-results-tip">试试其他关键词吧</p>
            </div>

            <div v-else class="search-results-list">
              <div
                v-for="anime in searchResults"
                :key="anime.vod_id"
                class="search-result-item"
                @click="viewAnimeDetail(anime)"
              >
                <div class="result-poster">
                  <img
                    v-if="anime.vod_pic"
                    :src="anime.vod_pic"
                    :alt="anime.vod_name"
                    @error="$event.target.style.display='none'"
                  />
                  <div v-else class="placeholder-poster">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </div>
                <div class="result-info">
                  <h4 class="result-title">{{ anime.vod_name }}</h4>
                  <p class="result-meta">
                    <span v-if="anime.vod_year">{{ anime.vod_year }}年</span>
                    <span v-if="anime.type_name">{{ anime.type_name }}</span>
                  </p>
                  <p class="result-desc" v-if="anime.vod_blurb">{{ anime.vod_blurb }}</p>
                </div>
              </div>
            </div>

            <div v-if="searchResults.length > 0" class="search-results-footer">
              <el-button
                text
                type="primary"
                @click="handleSearch"
                :disabled="isSearching"
              >
                查看完整搜索结果 ({{ searchResults.length }}+)
              </el-button>
            </div>
          </div>
        </div>

        <!-- 用户区域 -->
        <div class="user-area">
          <template v-if="isLoggedIn">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userInfo?.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ authStore.userDisplayName }}</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
                  <el-dropdown-item command="history">观看历史</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button @click="$router.push('/login')">登录</el-button>
            <el-button type="primary" @click="$router.push('/register')">注册</el-button>
          </template>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <router-view />
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-container">
        <p>&copy; 2024 动漫网站. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* 全局样式 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  align-items: center;
  height: 70px;
}

.logo {
  cursor: pointer;
  margin-right: 50px;
}

.logo h1 {
  margin: 0;
  font-size: 1.8rem;
  color: white;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-menu {
  display: flex;
  gap: 40px;
  margin-right: auto;
}

.nav-item {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 1rem;
  padding: 10px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transform: translateY(-2px);
}

.nav-item.active {
  color: white;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
}

.search-box {
  margin-right: 30px;
  position: relative;
}

.search-input {
  width: 350px;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 25px;
}

.search-input :deep(.el-input__inner) {
  color: white;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.7);
}

.search-icon {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.search-icon:hover {
  color: white;
}

.search-icon.searching {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 搜索结果下拉框样式 */
.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  max-height: 500px;
  overflow: hidden;
  margin-top: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-weight: 600;
  color: #333;
  min-height: 50px;
}

.search-results-header span {
  display: flex;
  align-items: center;
}

.search-results-header span:first-child::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #667eea;
  margin-right: 8px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.close-icon {
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease;
}

.close-icon:hover {
  color: #333;
}

.no-results {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.no-results p {
  margin: 0 0 8px 0;
}

.no-results-tip {
  font-size: 0.9rem;
  color: #999;
}

.search-results-list {
  max-height: 350px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-result-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateX(5px);
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-poster {
  width: 60px;
  height: 80px;
  flex-shrink: 0;
  margin-right: 15px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.result-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-poster {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.result-info {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-meta {
  font-size: 0.85rem;
  color: #666;
  margin: 0 0 8px 0;
}

.result-meta span {
  margin-right: 10px;
}

.result-desc {
  font-size: 0.8rem;
  color: #888;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-results-footer {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.user-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-area :deep(.el-button) {
  border-radius: 20px;
  font-weight: 600;
  padding: 10px 20px;
}

.user-area :deep(.el-button:not(.el-button--primary)) {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.user-area :deep(.el-button:not(.el-button--primary):hover) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

.user-area :deep(.el-button--primary) {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 25px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.username {
  font-weight: 600;
  color: white;
  font-size: 0.95rem;
}

/* 主内容区域 */
.main-content {
  flex: 1;
}

/* 底部样式 */
.footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 40px 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer p {
  margin: 0;
  font-size: 0.95rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-container {
    max-width: 1000px;
    padding: 0 30px;
  }

  .search-input {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 20px;
    height: 60px;
  }

  .logo {
    margin-right: 20px;
  }

  .logo h1 {
    font-size: 1.4rem;
  }

  .nav-menu {
    display: none;
  }

  .search-input {
    width: 200px;
  }

  .user-area {
    gap: 10px;
  }

  .user-area :deep(.el-button) {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}
</style>
