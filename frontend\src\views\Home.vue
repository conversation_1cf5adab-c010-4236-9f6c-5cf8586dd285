<!-- 首页组件 - 展示热门动漫、最新更新等内容 -->
<template>
  <div class="home">
    <!-- 轮播图区域 - 海报在左侧，文字在右侧 -->
    <el-carousel height="500px" class="banner-carousel" indicator-position="outside" arrow="hover">
      <el-carousel-item v-for="item in bannerList" :key="item.id">
        <div class="banner-item">
          <!-- 整个轮播区域的遮罩 -->
          <div class="banner-mask"></div>

          <!-- 海报区域 -->
          <div class="banner-poster">
            <img :src="item.image" :alt="item.title" />
          </div>

          <!-- 文字内容区域 -->
          <div class="banner-content">
            <div class="banner-text">
              <h1>{{ item.title }}</h1>
              <p class="banner-description">{{ item.description }}</p>
              <div class="banner-actions">
                <el-button type="primary" size="large" @click="goToAnime(item)" class="info-btn">
                  <el-icon><InfoFilled /></el-icon>
                  详细信息
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 内容区域 - 优化PC端布局 -->
    <div class="content-container">
      <!-- 快速导航 -->
      <section class="quick-nav-section">
        <div class="quick-nav">
          <div class="nav-item" @click="$router.push('/anime?sort=popular')">
            <el-icon class="nav-icon"><TrendCharts /></el-icon>
            <span>热门排行</span>
          </div>
          <div class="nav-item" @click="$router.push('/anime?sort=latest')">
            <el-icon class="nav-icon"><Clock /></el-icon>
            <span>最新更新</span>
          </div>
          <div class="nav-item" @click="$router.push('/anime?status=completed')">
            <el-icon class="nav-icon"><Check /></el-icon>
            <span>完结动漫</span>
          </div>
          <div class="nav-item" @click="$router.push('/anime?category=movie')">
            <el-icon class="nav-icon"><Film /></el-icon>
            <span>动漫电影</span>
          </div>
        </div>
      </section>

      <!-- 热门推荐 -->
      <section class="section">
        <div class="section-header">
          <div class="section-title">
            <el-icon class="section-icon"><Star /></el-icon>
            <h3>热门推荐</h3>
          </div>
          <el-button text type="primary" @click="$router.push('/anime?sort=popular')" class="more-btn">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="anime-grid">
          <div v-for="anime in popularAnime" :key="anime.id" class="anime-card" @click="goToAnime(anime)">
            <div class="anime-poster">
              <img :src="anime.poster" :alt="anime.title" />
              <div class="anime-overlay">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
                <!-- 悬停时显示年份 - 位置在底部 -->
                <div class="anime-year" v-if="anime.year">
                  {{ anime.year }}年
                </div>
              </div>
              <div class="anime-badge" v-if="anime.isFeatured">推荐</div>
              <div class="source-badge" v-if="anime.sourceName">{{ anime.sourceName }}</div>
            </div>
            <div class="anime-info">
              <h4 class="anime-title">{{ anime.title }}</h4>
              <p class="anime-meta">{{ anime.year }} · {{ anime.category }}</p>
              <div class="anime-rating">
                <el-rate v-model="anime.rating" disabled show-score size="small" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 最新更新 -->
      <section class="section">
        <div class="section-header">
          <div class="section-title">
            <el-icon class="section-icon"><Refresh /></el-icon>
            <h3>最新更新</h3>
          </div>
          <el-button text type="primary" @click="$router.push('/anime?sort=latest')" class="more-btn">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="anime-grid">
          <div v-for="anime in latestAnime" :key="anime.id" class="anime-card" @click="goToAnime(anime)">
            <div class="anime-poster">
              <img :src="anime.poster" :alt="anime.title" />
              <div class="anime-overlay">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
                <!-- 悬停时显示年份 - 位置在底部 -->
                <div class="anime-year" v-if="anime.year">
                  {{ anime.year }}年
                </div>
              </div>
              <div class="episode-badge">第{{ anime.latestEpisode }}集</div>
            </div>
            <div class="anime-info">
              <h4 class="anime-title">{{ anime.title }}</h4>
              <p class="anime-meta">更新至第{{ anime.latestEpisode }}集</p>
              <div class="anime-rating">
                <el-rate v-model="anime.rating" disabled show-score size="small" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 分类推荐 -->
      <section class="section">
        <div class="section-header">
          <div class="section-title">
            <el-icon class="section-icon"><Grid /></el-icon>
            <h3>分类推荐</h3>
          </div>
        </div>
        <div class="category-grid">
          <div v-for="category in categories" :key="category.id" class="category-card" @click="$router.push(`/anime?category=${category.name}`)">
            <div class="category-image" :style="{ backgroundImage: `url(${category.image})` }">
              <div class="category-overlay">
                <h4>{{ category.name }}</h4>
                <p>{{ category.count }}部作品</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { animeAPI, categoryAPI, externalAPI, handleApiError } from '@/services/api'
import {
  ArrowRight,
  VideoPlay,
  InfoFilled,
  TrendCharts,
  Clock,
  Check,
  Film,
  Star,
  Refresh,
  Grid
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const bannerList = ref([])
const popularAnime = ref([])
const latestAnime = ref([])
const categories = ref([])
const loading = ref(false)

// 跳转到动漫详情页
const goToAnime = (anime) => {
  // 统一跳转到动漫详情页面
  const animeId = typeof anime === 'object' ? anime.id : anime
  router.push(`/anime/${animeId}`)
}



// 获取首页数据
const fetchHomeData = async () => {
  try {
    loading.value = true

    // 优先获取外部资源数据（索尼资源优先），如果失败则使用本地数据
    try {
      // 获取外部热门动漫数据（量子资源优先）
      const externalPopularResponse = await externalAPI.getPopularAnime(1, 9)

      if (externalPopularResponse && externalPopularResponse.code === 1 && externalPopularResponse.list) {
        // 设置热门动漫数据（来自外部资源）
        popularAnime.value = externalPopularResponse.list.map(anime => ({
          id: anime.vod_id,
          title: anime.vod_name,
          poster: anime.vod_pic,
          year: anime.vod_year,
          category: anime.type_name || '动漫',
          rating: parseFloat(anime.vod_score) || 0,
          isFeatured: anime.sourceKey === 'lzzy', // 量子资源标记为推荐
          sourceKey: anime.sourceKey,
          sourceName: anime.sourceName,
          description: anime.vod_blurb || anime.vod_content
        }))

        // 设置轮播图数据（使用前3个热门动漫）
        bannerList.value = popularAnime.value.slice(0, 3).map(anime => ({
          id: anime.id,
          title: anime.title,
          description: anime.description || `来自${anime.sourceName}的精彩动漫`,
          image: anime.poster
        }))

        // 设置最新动漫数据（使用外部资源数据）
        const externalLatestResponse = await externalAPI.getAnimeList('日韩动漫', 1)
        if (externalLatestResponse && externalLatestResponse.code === 1 && externalLatestResponse.list) {
          latestAnime.value = externalLatestResponse.list.slice(0, 8).map(anime => ({
            id: anime.vod_id,
            title: anime.vod_name,
            poster: anime.vod_pic,
            latestEpisode: anime.vod_remarks || '更新中',
            rating: parseFloat(anime.vod_score) || 0,
            sourceKey: anime.sourceKey,
            sourceName: anime.sourceName
          }))
        }

        console.log('成功获取外部资源数据，量子资源优先展示')
      } else {
        throw new Error('外部资源数据获取失败')
      }
    } catch (externalError) {
      console.warn('外部资源获取失败，使用本地数据:', externalError)

      // 如果外部资源失败，使用本地API数据
      const [featuredResponse, popularResponse, latestResponse] = await Promise.all([
        animeAPI.getFeatured(),
        animeAPI.getPopular(12),
        animeAPI.getLatest(12)
      ])

      // 设置轮播图数据（使用推荐动漫）
      bannerList.value = featuredResponse.slice(0, 3).map(anime => ({
        id: anime.id,
        title: anime.title,
        description: anime.description,
        image: anime.banner || anime.poster
      }))

      // 设置热门动漫数据
      popularAnime.value = popularResponse.map(anime => ({
        id: anime.id,
        title: anime.title,
        poster: anime.poster,
        year: anime.year,
        category: anime.status,
        rating: anime.rating || 0,
        isFeatured: anime.isFeatured
      }))

      // 设置最新动漫数据
      latestAnime.value = latestResponse.map(anime => ({
        id: anime.id,
        title: anime.title,
        poster: anime.poster,
        latestEpisode: anime.currentEpisodes || 1,
        rating: anime.rating || 0
      }))
    }

    // 获取分类数据（使用本地API）
    try {
      const categoriesResponse = await categoryAPI.getAll()
      categories.value = categoriesResponse.map(category => ({
        id: category.id,
        name: category.name,
        count: Math.floor(Math.random() * 200) + 50,
        image: category.image
      }))
    } catch (categoryError) {
      console.warn('分类数据获取失败:', categoryError)
      // 使用默认分类数据
      categories.value = [
        { id: 1, name: '日本动漫', count: 156, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
        { id: 2, name: '中国动漫', count: 89, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
        { id: 3, name: '欧美动漫', count: 124, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
        { id: 4, name: '动画片', count: 203, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' }
      ]
    }

  } catch (error) {
    console.error('获取首页数据失败:', error)
    ElMessage.error(handleApiError(error))

    // 如果所有API都失败，使用备用数据
    setFallbackData()
  } finally {
    loading.value = false
  }
}

// 备用数据（API失败时使用）
const setFallbackData = () => {
  bannerList.value = [
    {
      id: 1,
      title: '进击的巨人 最终季',
      description: '人类与巨人的终极对决，真相即将揭晓',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=500&fit=crop'
    },
    {
      id: 2,
      title: '鬼灭之刃 刀匠村篇',
      description: '炭治郎前往刀匠村，新的冒险即将开始',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=500&fit=crop'
    }
  ]

  popularAnime.value = Array.from({ length: 8 }, (_, i) => ({
    id: i + 1,
    title: `热门动漫 ${i + 1}`,
    poster: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop',
    year: 2023,
    category: '动作',
    rating: 4.5,
    isFeatured: i < 3
  }))

  latestAnime.value = Array.from({ length: 8 }, (_, i) => ({
    id: i + 13,
    title: `最新动漫 ${i + 1}`,
    poster: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop',
    latestEpisode: i + 1,
    rating: 4.0
  }))

  categories.value = [
    { id: 1, name: '动作', count: 156, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
    { id: 2, name: '冒险', count: 89, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
    { id: 3, name: '喜剧', count: 124, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' },
    { id: 4, name: '剧情', count: 203, image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop' }
  ]
}

// 组件挂载时获取数据
onMounted(() => {
  fetchHomeData()
})
</script>

<style scoped>
/* PC端优化样式 */
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 轮播图样式优化 */
.banner-carousel {
  margin-bottom: 60px;
  border-radius: 0 0 20px 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.banner-item {
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  overflow: hidden;
}

/* 整个轮播区域的遮罩 */
.banner-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 1;
}

/* 海报区域样式 */
.banner-poster {
  width: 380px;
  height: 100%;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  padding: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-poster img {
  width: 300px;
  height: 450px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease;
}

.banner-poster img:hover {
  transform: scale(1.05);
}

.banner-content {
  flex: 1;
  padding: 40px 60px 40px 40px;
  color: white;
  z-index: 2;
  position: relative;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  padding-top: 440px; /* 大幅增加顶部padding，为-400px的margin腾出空间 */
}

.banner-text h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
  margin-top: -400px; /* 大幅上移到轮播区域顶部附近 */
}

.banner-description {
  font-size: 1.4rem;
  margin-bottom: 2.5rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-top: 60px; /* 让简介下移 */
  max-width: 860px; /* 再增加宽度，让每行多显示约三个字 */
  word-wrap: break-word; /* 确保长单词能够换行 */
}

.banner-actions {
  display: flex;
  gap: 20px;
  position: absolute; /* 使用绝对定位固定按钮位置 */
  bottom: 80px; /* 距离轮播区域底部80px的固定位置 */
  left: 40px; /* 与文字内容左对齐 */
}

.info-btn {
  padding: 12px 30px;
  font-size: 1.1rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 与项目整体蓝紫色调一致 */
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); /* 蓝紫色阴影 */
}

.info-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%); /* 悬停时颜色更深的蓝紫色 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6); /* 悬停时阴影更明显 */
}

/* 内容区域样式 */
.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

/* 快速导航样式 */
.quick-nav-section {
  margin-bottom: 60px;
}

.quick-nav {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.nav-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.nav-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #667eea;
  transition: color 0.3s ease;
}

.nav-item:hover .nav-icon {
  color: white;
}

.nav-item span {
  font-size: 1.1rem;
  font-weight: 600;
}

/* 区块样式 */
.section {
  margin-bottom: 80px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 0 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-icon {
  font-size: 2rem;
  color: #667eea;
}

.section-header h3 {
  font-size: 2.2rem;
  color: #2c3e50;
  font-weight: 700;
  margin: 0;
}

.more-btn {
  font-size: 1.1rem;
  font-weight: 600;
}

/* 动漫网格样式 - 一行显示3部 */
.anime-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 20px;
}

.anime-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.anime-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.anime-poster {
  position: relative;
  overflow: hidden;
  height: 300px;
}

.anime-poster img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.anime-card:hover .anime-poster img {
  transform: scale(1.05);
}

.anime-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 20px;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none; /* 防止遮罩层阻止点击事件 */
}

.anime-card:hover .anime-overlay {
  opacity: 1;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 4rem;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* 年份显示样式 - 位置在底部 */
.anime-year {
  color: white;
  font-size: 14px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.anime-badge, .episode-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

.episode-badge {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  box-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
}

.source-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 3;
}

.anime-info {
  padding: 20px;
}

.anime-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.anime-meta {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.anime-rating {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 分类网格样式 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  padding: 20px;
}

.category-card {
  height: 180px;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.category-image {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  transition: all 0.3s ease;
}

.category-card:hover .category-image::before {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
}

.category-overlay {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.category-overlay h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-overlay p {
  font-size: 1rem;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-container {
    max-width: 1000px;
    padding: 0 30px;
  }

  .anime-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .content-container {
    padding: 0 20px;
  }

  .banner-item {
    flex-direction: column;
    padding: 0;
  }

  .banner-poster {
    width: 100%;
    height: 200px;
    padding: 20px;
  }

  .banner-content {
    padding: 20px;
    text-align: center;
  }

  .banner-text h1 {
    font-size: 2.5rem;
  }

  .banner-description {
    font-size: 1.1rem;
    max-width: 645px; /* 移动端也相应增加宽度 */
    margin: 0 auto 2.5rem auto; /* 居中显示 */
  }

  .banner-actions {
    flex-direction: column;
    gap: 15px;
    position: absolute; /* 移动端也使用绝对定位 */
    bottom: 40px; /* 移动端距离底部40px */
    left: 50%; /* 水平居中 */
    transform: translateX(-50%); /* 完全居中 */
    width: auto; /* 自适应宽度 */
  }

  .quick-nav {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 30px;
  }

  .anime-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>
