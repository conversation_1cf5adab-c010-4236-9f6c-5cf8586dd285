@import './base.css';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: #ffffff;
}

#app {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

/* Element Plus 轮播图样式修复 */
.el-carousel__container {
  height: 500px !important;
}

.el-carousel__item {
  height: 500px !important;
}

/* 轮播图指示器样式 */
.el-carousel__indicators--outside {
  margin-top: 20px;
}

.el-carousel__indicator {
  padding: 8px 4px;
}

.el-carousel__button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid transparent;
}

.el-carousel__indicator.is-active .el-carousel__button {
  background-color: #667eea;
}

/* 链接样式 */
a {
  text-decoration: none;
  color: #667eea;
  transition: color 0.3s ease;
}

a:hover {
  color: #764ba2;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
