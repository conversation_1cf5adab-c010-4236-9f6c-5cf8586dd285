<!-- 外部动漫播放页面 - 播放豪华资源的动漫视频 -->
<template>
  <div class="external-play-page">
    <!-- 返回按钮 -->
    <div class="back-section">
      <el-button @click="goBack" :icon="ArrowLeft">
        返回列表
      </el-button>
    </div>

    <!-- 动漫标题 -->
    <div class="anime-title-section" v-if="animeTitle">
      <h1>{{ animeTitle }}</h1>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-section">
      <el-skeleton :rows="5" animated />
      <div class="loading-text">正在获取播放信息...</div>
    </div>

    <!-- 播放区域 -->
    <div v-else-if="playData" class="play-section">
      <!-- 播放源选择 -->
      <div class="source-selector" v-if="Object.keys(playData.playUrls).length > 1">
        <h3>选择播放源：</h3>
        <el-radio-group v-model="selectedSource" @change="onSourceChange">
          <el-radio
            v-for="(episodes, sourceName) in playData.playUrls"
            :key="sourceName"
            :label="sourceName"
            :value="sourceName"
          >
            {{ sourceName }} ({{ episodes.length }}集)
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 剧集选择 -->
      <div class="episode-selector" v-if="currentEpisodes.length > 0">
        <h3>选择剧集：</h3>
        <div class="episode-list">
          <el-button
            v-for="(episode, index) in currentEpisodes"
            :key="index"
            :type="currentEpisodeIndex === index ? 'primary' : 'default'"
            size="small"
            @click="selectEpisode(index)"
            class="episode-btn"
          >
            {{ episode.name }}
          </el-button>
        </div>
      </div>

      <!-- 播放器区域 -->
      <div class="player-section" v-if="currentPlayUrl">
        <!-- 播放状态提示 -->
        <div class="player-status" v-if="playerStatus" style="margin-bottom: 15px;">
          <el-alert
            :title="playerStatus.title"
            :type="playerStatus.type"
            :description="playerStatus.message"
            :closable="false"
            show-icon
          />
        </div>

        <div class="player-container">
          <!-- HLS播放器 -->
          <video
            ref="videoPlayer"
            controls
            width="100%"
            height="500"
            :poster="animePoster"
            @loadedmetadata="onVideoLoaded"
            @timeupdate="onTimeUpdate"
            @ended="onVideoEnded"
            @error="onVideoError"
            @loadstart="onLoadStart"
            @canplay="onCanPlay"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 播放信息 -->
        <div class="play-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <strong>当前播放：</strong>
                {{ currentEpisodes[currentEpisodeIndex]?.name || '未知' }}
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <strong>播放源：</strong>
                {{ selectedSource }}
              </div>
            </el-col>
          </el-row>
          
          <div class="play-url-info">
            <strong>播放地址：</strong>
            <el-input
              :value="currentPlayUrl"
              readonly
              size="small"
            >
              <template #append>
                <el-button @click="copyPlayUrl" size="small">
                  复制
                </el-button>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 播放控制 -->
        <div class="play-controls">
          <el-button
            @click="playPrevious"
            :disabled="currentEpisodeIndex === 0"
            :icon="DArrowLeft"
          >
            上一集
          </el-button>
          
          <el-button
            @click="togglePlay"
            :icon="isPlaying ? VideoPause : VideoPlay"
            type="primary"
          >
            {{ isPlaying ? '暂停' : '播放' }}
          </el-button>
          
          <el-button
            @click="playNext"
            :disabled="currentEpisodeIndex === currentEpisodes.length - 1"
            :icon="DArrowRight"
          >
            下一集
          </el-button>
        </div>
      </div>

      <!-- 播放提示 -->
      <div class="play-tips">
        <el-alert
          title="播放提示"
          type="info"
          :closable="false"
        >
          <ul>
            <li>支持HLS(.m3u8)流媒体播放</li>
            <li>如果无法播放，请尝试切换播放源</li>
            <li>建议使用Chrome或Firefox浏览器</li>
            <li>播放进度会自动保存</li>
          </ul>
        </el-alert>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-section">
      <el-result
        icon="error"
        title="获取播放信息失败"
        sub-title="请检查网络连接或稍后重试"
      >
        <template #extra>
          <el-button type="primary" @click="retryLoad">
            重新加载
          </el-button>
          <el-button @click="goBack">
            返回列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  VideoPlay, 
  VideoPause, 
  DArrowLeft, 
  DArrowRight 
} from '@element-plus/icons-vue'
import { externalAPI } from '@/services/api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const isPlaying = ref(false)
const playData = ref(null)
const selectedSource = ref('')
const currentEpisodeIndex = ref(0)
const currentPlayUrl = ref('')
const videoPlayer = ref(null)
const animeTitle = ref(route.query.title || '未知动漫')
const playerStatus = ref(null)
const animePoster = ref('')

// 计算属性
const currentEpisodes = computed(() => {
  if (!playData.value || !selectedSource.value) return []
  return playData.value.playUrls[selectedSource.value] || []
})

// 获取播放数据
const fetchPlayData = async () => {
  const vodId = route.query.id
  if (!vodId) {
    ElMessage.error('缺少动漫ID参数')
    goBack()
    return
  }

  try {
    isLoading.value = true
    console.log('获取播放数据:', vodId)

    const response = await externalAPI.getPlayUrls(vodId)
    
    if (response && response.playUrls) {
      playData.value = response
      animeTitle.value = response.title || animeTitle.value
      
      // 选择默认播放源
      const sources = Object.keys(response.playUrls)
      if (sources.length > 0) {
        // 优先选择hhm3u8源（HLS格式）
        selectedSource.value = sources.includes('hhm3u8') ? 'hhm3u8' : sources[0]
        
        // 选择第一集
        if (currentEpisodes.value.length > 0) {
          selectEpisode(0)
        }
      }
      
      console.log('播放数据获取成功:', response)
    } else {
      throw new Error('播放数据格式错误')
    }

  } catch (error) {
    console.error('获取播放数据失败:', error)
    ElMessage.error('获取播放信息失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 选择剧集
const selectEpisode = async (index) => {
  if (index < 0 || index >= currentEpisodes.value.length) return
  
  currentEpisodeIndex.value = index
  const episode = currentEpisodes.value[index]
  currentPlayUrl.value = episode.url
  
  console.log('选择剧集:', episode.name, episode.url)
  
  // 等待DOM更新后设置视频源
  await nextTick()
  setupVideoPlayer()
}

// 设置视频播放器
const setupVideoPlayer = () => {
  if (!videoPlayer.value || !currentPlayUrl.value) return

  try {
    console.log('设置播放器，URL:', currentPlayUrl.value)

    // 清理之前的播放器
    if (window.currentHls) {
      window.currentHls.destroy()
      window.currentHls = null
    }

    // 检查是否是HLS格式
    if (currentPlayUrl.value.includes('.m3u8')) {
      console.log('检测到HLS格式，尝试使用HLS.js')

      // 尝试使用HLS.js
      if (window.Hls && window.Hls.isSupported()) {
        console.log('HLS.js支持，创建播放器')

        const hls = new window.Hls({
          debug: false,
          enableWorker: true,
          lowLatencyMode: false,
          backBufferLength: 90,
          maxBufferLength: 30,
          maxMaxBufferLength: 600,
          maxBufferSize: 60 * 1000 * 1000,
          maxBufferHole: 0.5,
          highBufferWatchdogPeriod: 2,
          nudgeOffset: 0.1,
          nudgeMaxRetry: 3,
          maxFragLookUpTolerance: 0.25,
          liveSyncDurationCount: 3,
          liveMaxLatencyDurationCount: 10,
          liveDurationInfinity: false,
          enableSoftwareAES: true,
          manifestLoadingTimeOut: 10000,
          manifestLoadingMaxRetry: 1,
          manifestLoadingRetryDelay: 1000,
          levelLoadingTimeOut: 10000,
          levelLoadingMaxRetry: 4,
          levelLoadingRetryDelay: 1000,
          fragLoadingTimeOut: 20000,
          fragLoadingMaxRetry: 6,
          fragLoadingRetryDelay: 1000,
          startFragPrefetch: true,
          testBandwidth: true
        })

        window.currentHls = hls

        hls.loadSource(currentPlayUrl.value)
        hls.attachMedia(videoPlayer.value)

        hls.on(window.Hls.Events.MANIFEST_PARSED, () => {
          console.log('HLS流清单解析完成')
          ElMessage.success('视频加载成功')
        })

        hls.on(window.Hls.Events.LEVEL_LOADED, () => {
          console.log('HLS级别加载完成')
        })

        hls.on(window.Hls.Events.FRAG_LOADED, () => {
          console.log('视频片段加载完成')
        })

        hls.on(window.Hls.Events.ERROR, (event, data) => {
          console.error('HLS播放错误:', event, data)

          if (data.fatal) {
            switch (data.type) {
              case window.Hls.ErrorTypes.NETWORK_ERROR:
                console.error('网络错误，尝试恢复')
                ElMessage.error('网络错误，请检查网络连接')
                hls.startLoad()
                break
              case window.Hls.ErrorTypes.MEDIA_ERROR:
                console.error('媒体错误，尝试恢复')
                ElMessage.error('媒体解码错误，尝试恢复中...')
                hls.recoverMediaError()
                break
              default:
                console.error('无法恢复的错误')
                ElMessage.error('视频播放失败，请尝试其他播放源')
                hls.destroy()
                break
            }
          } else {
            console.warn('非致命HLS错误:', data)
          }
        })

      } else if (videoPlayer.value.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持
        console.log('使用Safari原生HLS支持')
        videoPlayer.value.src = currentPlayUrl.value
        ElMessage.success('使用原生播放器加载视频')
      } else {
        console.error('浏览器不支持HLS播放')
        ElMessage.error('浏览器不支持HLS播放，请使用Chrome、Firefox或Safari浏览器')
      }
    } else {
      // 普通视频格式
      console.log('使用普通视频格式')
      videoPlayer.value.src = currentPlayUrl.value
    }
  } catch (error) {
    console.error('设置播放器失败:', error)
    ElMessage.error('播放器设置失败: ' + error.message)
  }
}

// 播放源变化
const onSourceChange = () => {
  if (currentEpisodes.value.length > 0) {
    selectEpisode(0) // 切换源时回到第一集
  }
}

// 播放控制
const togglePlay = () => {
  if (!videoPlayer.value) return
  
  if (videoPlayer.value.paused) {
    videoPlayer.value.play()
  } else {
    videoPlayer.value.pause()
  }
}

const playPrevious = () => {
  if (currentEpisodeIndex.value > 0) {
    selectEpisode(currentEpisodeIndex.value - 1)
  }
}

const playNext = () => {
  if (currentEpisodeIndex.value < currentEpisodes.value.length - 1) {
    selectEpisode(currentEpisodeIndex.value + 1)
  }
}

// 视频事件处理
const onVideoLoaded = () => {
  console.log('视频元数据加载完成')
}

const onTimeUpdate = () => {
  // 这里可以保存播放进度
  if (videoPlayer.value) {
    isPlaying.value = !videoPlayer.value.paused
  }
}

const onVideoEnded = () => {
  console.log('视频播放结束')
  isPlaying.value = false
  
  // 自动播放下一集
  if (currentEpisodeIndex.value < currentEpisodes.value.length - 1) {
    ElMessage.success('自动播放下一集')
    setTimeout(() => {
      playNext()
    }, 2000)
  }
}

const onVideoError = (event) => {
  console.error('视频播放错误:', event)
  setPlayerStatus('error', '播放错误', '视频播放出错，请尝试其他播放源或刷新页面')
  ElMessage.error('视频播放出错，请尝试其他播放源')
}

// 新增事件处理
const onLoadStart = () => {
  console.log('开始加载视频')
  setPlayerStatus('info', '正在加载', '视频正在加载中，请稍候...')
}

const onCanPlay = () => {
  console.log('视频可以播放')
  setPlayerStatus('success', '加载完成', '视频已准备就绪，可以开始播放')
  setTimeout(() => {
    playerStatus.value = null
  }, 3000)
}

// 设置播放器状态
const setPlayerStatus = (type, title, message) => {
  playerStatus.value = {
    type,
    title,
    message
  }
}

// 复制播放链接
const copyPlayUrl = async () => {
  try {
    await navigator.clipboard.writeText(currentPlayUrl.value)
    ElMessage.success('播放链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 返回列表
const goBack = () => {
  router.push('/external')
}

// 重新加载
const retryLoad = () => {
  fetchPlayData()
}

// 组件挂载
onMounted(async () => {
  // 确保HLS.js加载完成
  await loadHlsJs()

  // 获取播放数据
  fetchPlayData()
})

// 加载HLS.js
const loadHlsJs = () => {
  return new Promise((resolve) => {
    if (window.Hls) {
      console.log('HLS.js已存在')
      resolve()
      return
    }

    console.log('加载HLS.js...')
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/npm/hls.js@latest'
    script.onload = () => {
      console.log('HLS.js加载完成')
      resolve()
    }
    script.onerror = () => {
      console.error('HLS.js加载失败')
      ElMessage.error('播放器组件加载失败')
      resolve()
    }
    document.head.appendChild(script)
  })
}

// 组件卸载
onUnmounted(() => {
  // 清理视频播放器
  if (videoPlayer.value) {
    videoPlayer.value.pause()
    videoPlayer.value.src = ''
  }
})
</script>

<style scoped>
.external-play-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-section {
  margin-bottom: 20px;
}

.anime-title-section {
  text-align: center;
  margin-bottom: 30px;
}

.anime-title-section h1 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.loading-section {
  text-align: center;
  padding: 50px;
}

.loading-text {
  margin-top: 20px;
  color: #666;
  font-size: 1.1rem;
}

.source-selector,
.episode-selector {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.source-selector h3,
.episode-selector h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.episode-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.episode-btn {
  min-width: 80px;
}

.player-section {
  margin-bottom: 30px;
}

.player-container {
  margin-bottom: 20px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.play-info {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 10px;
  color: #333;
}

.play-url-info {
  margin-top: 15px;
}

.play-controls {
  text-align: center;
  margin-bottom: 20px;
}

.play-controls .el-button {
  margin: 0 10px;
}

.play-tips {
  margin-top: 30px;
}

.play-tips ul {
  margin: 10px 0 0 20px;
  color: #666;
}

.play-tips li {
  margin-bottom: 5px;
}

.error-section {
  text-align: center;
  padding: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .external-play-page {
    padding: 10px;
  }
  
  .episode-list {
    justify-content: center;
  }
  
  .play-controls .el-button {
    margin: 5px;
  }
}
</style>
